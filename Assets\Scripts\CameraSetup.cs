using UnityEngine;
using GridSystem;

[ExecuteInEditMode]
public class CameraSetup : MonoBehaviour
{
    public GridManager gridManager;
    public float panSpeed = 0.01f;

    private Vector2 lastPanPosition;
    public bool IsPanning { get; private set; }

    private bool blockPanning = false;

    /// <summary>
    /// Sürükleme sırasında kamera kaymasını engelle.
    /// </summary>
    public void BlockPanningDuringDrag(bool block)
    {
        blockPanning = block;
    }

    void Start()
    {
        if (gridManager == null)
        {
            gridManager = FindObjectOfType<GridManager>();
        }
        CenterCameraOnGrid();
    }

    void OnValidate()
    {
        if (gridManager != null)
        {
            CenterCameraOnGrid();
        }
    }

    void Update()
    {
        if (Application.isPlaying)
        {
            HandlePanning();
        }
        else
        {
            CenterCameraOnGrid();
        }
    }

    void HandlePanning()
    {
        if (blockPanning) return;

        // Dokunmatik kontrol
        if (Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);

            if (touch.phase == TouchPhase.Began)
            {
                IsPanning = true;
                lastPanPosition = touch.position;
            }
            else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
            {
                IsPanning = false;
            }
            else if (touch.phase == TouchPhase.Moved && IsPanning)
            {
                PanCamera(touch.position);
            }
        }
        else // Mouse kontrol
        {
            if (Input.GetMouseButtonDown(0))
            {
                IsPanning = true;
                lastPanPosition = Input.mousePosition;
            }
            else if (Input.GetMouseButtonUp(0))
            {
                IsPanning = false;
            }
            else if (Input.GetMouseButton(0) && IsPanning)
            {
                PanCamera(Input.mousePosition);
            }
        }
    }

    void PanCamera(Vector2 newPosition)
    {
        Vector2 delta = newPosition - lastPanPosition;

        Vector3 movement = new Vector3(
            -delta.x * panSpeed,
            0f,
            -delta.y * panSpeed
        );

        // Kamera yönüne göre hareketi düzelt
        movement = Quaternion.Euler(0, transform.eulerAngles.y, 0) * movement;

        transform.position += movement;
        lastPanPosition = newPosition;
    }

    void CenterCameraOnGrid()
    {
        if (gridManager == null) return;

        // Grid'in ortasını hesapla
        Vector3 gridCenter = gridManager.transform.position + new Vector3(
            gridManager.gridSize.x * gridManager.cellSize * 0.5f,
            0f,
            gridManager.gridSize.y * gridManager.cellSize * 0.5f
        );

        // Kamera yüksekliği ve uzaklığı ayarla
        float distance = Mathf.Max(gridManager.gridSize.x, gridManager.gridSize.y) * gridManager.cellSize * 0.8f;

        // Kamera rotasyonuna göre offset oluştur
        Quaternion rotation = Quaternion.Euler(transform.eulerAngles);
        Vector3 offset = rotation * new Vector3(0f, distance, -distance);

        transform.position = gridCenter + offset;
    }
}
