using System;
using System.Collections.Generic;
using UnityEngine.Events;

public class EventManager
{
    private static Dictionary<string, object> eventDictionaryByName = new Dictionary<string, object>();

    private static void Log(string message)
    {
        // UnityEngine.Debug.Log($"[Event] {message} is Invoked");
    }

    public static void Invoke(string name)
    {
        Log(name);
        if (eventDictionaryByName.ContainsKey(name))
        {
            UnityEvent eventWithName = eventDictionaryByName[name] as UnityEvent;
            eventWithName?.Invoke();
        }

    }

    public static void Invoke<T0>(string name, T0 value)
    {
        Log(name);

        if (eventDictionaryByName.ContainsKey(name))
        {
            UnityEvent<T0> eventWithName = eventDictionaryByName[name] as UnityEvent<T0>;
            eventWithName?.Invoke(value);
        }

    }
    public static void Invoke<T0>(T0 param)
    {
        Type eventType = typeof(T0);
        Invoke(eventType.Name, param);
    }
    public static void Invoke<T0, T1>(string name, T0 param, T1 client)
    {
        if (eventDictionaryByName.ContainsKey(name))
        {
            var eventWithName = eventDictionaryByName[name] as UnityEvent<T0, T1>;
            eventWithName?.Invoke(param, client);
        }
    }
    public static void Invoke<T0, T1>(T0 param, T1 client)
    {
        var key = typeof(T0).Name + typeof(T1).Name;
        if (eventDictionaryByName.ContainsKey(key))
        {
            var eventWithName = eventDictionaryByName[key] as UnityEvent<T0, T1>;
            eventWithName?.Invoke(param, client);
        }

    }
    public static void AddListener(string name, UnityAction callback)
    {
        Log(name);

        if (eventDictionaryByName.ContainsKey(name))
        {
            (eventDictionaryByName[name] as UnityEvent).AddListener(callback);
        }
        else
        {
            UnityEvent newEvent = new UnityEvent();
            newEvent.AddListener(callback);
            eventDictionaryByName.Add(name, newEvent);
        }
    }
    public static void AddListener<T0>(string name, UnityAction<T0> callback)
    {
        Log(name);

        if (eventDictionaryByName.ContainsKey(name))
        {
            (eventDictionaryByName[name] as UnityEvent<T0>).AddListener(callback);
        }
        else
        {
            var newEvent = new UnityEvent<T0>();
            newEvent.AddListener(callback);
            eventDictionaryByName.Add(name, newEvent);
        }
    }
    public static void AddListener<T0>(UnityAction<T0> callback)
    {
        var name = typeof(T0).Name;
        if (eventDictionaryByName.ContainsKey(name))
        {
            (eventDictionaryByName[name] as UnityEvent<T0>).AddListener(callback);
        }
        else
        {
            var newEvent = new UnityEvent<T0>();
            newEvent.AddListener(callback);
            eventDictionaryByName.Add(name, newEvent);
        }
    }
    public static void AddListener<T0, T1>(string name, UnityAction<T0, T1> callback)
    {
        if (eventDictionaryByName.ContainsKey(name))
        {
            (eventDictionaryByName[name] as UnityEvent<T0, T1>).AddListener(callback);
        }
        else
        {
            var newEvent = new UnityEvent<T0, T1>();
            newEvent.AddListener(callback);
            eventDictionaryByName.Add(name, newEvent);
        }
    }
    public static void AddListener<T0, T1>(UnityAction<T0, T1> callback)
    {
        var key = typeof(T0).Name + typeof(T1).Name;
        if (eventDictionaryByName.ContainsKey(key))
        {
            (eventDictionaryByName[key] as UnityEvent<T0, T1>).AddListener(callback);
        }
        else
        {
            var newEvent = new UnityEvent<T0, T1>();
            newEvent.AddListener(callback);
            eventDictionaryByName.Add(key, newEvent);
        }
    }
    public static void RemoveListener(string name, UnityAction callback)
    {
        if (eventDictionaryByName.ContainsKey(name))
        {
            UnityEvent eventWithType = eventDictionaryByName[name] as UnityEvent;
            eventWithType.RemoveListener(callback);
        }
    }
    public static void RemoveListener<T0>(string name, UnityAction<T0> callback)
    {
        if (eventDictionaryByName.ContainsKey(name))
        {
            UnityEvent<T0> eventWithType = eventDictionaryByName[name] as UnityEvent<T0>;
            eventWithType.RemoveListener(callback);

            // Eğer dinleyen kalmadıysa, eventi Dictionary'den sil
            //if (eventWithType.coun() == 0)
            //{
            //    eventDictionaryByName.Remove(name);
            //}
        }
    }
    public static void RemoveListener<T0>(UnityAction<T0> callback)
    {
        Type eventType = typeof(T0);
        if (eventDictionaryByName.ContainsKey(eventType.Name))
        {
            UnityEvent<T0> eventWithType = eventDictionaryByName[eventType.Name] as UnityEvent<T0>;
            eventWithType.RemoveListener(callback);

            // Eğer dinleyen kalmadıysa, eventi Dictionary'den sil
            //if (eventWithType.GetPersistentEventCount() == 0)
            //{
            //    eventDictionaryByName.Remove(eventType.Name);
            //}
        }
    }
    public static void RemoveListener<T0, T1>(UnityAction<T0, T1> callback)
    {
        var key = typeof(T0).Name + typeof(T1).Name;

        if (eventDictionaryByName.ContainsKey(key))
        {
            var eventWithType = eventDictionaryByName[key] as UnityEvent<T0, T1>;
            eventWithType.RemoveListener(callback);

            // Eğer dinleyen kalmadıysa, eventi Dictionary'den sil
            //if (eventWithType.GetPersistentEventCount() == 0)
            //{
            //    eventDictionaryByName.Remove(key);
            //}
        }
    }
    public static void RemoveListener<T0, T1>(string name, UnityAction<T0, T1> callback)
    {
        if (eventDictionaryByName.ContainsKey(name))
        {
            var eventWithType = eventDictionaryByName[name] as UnityEvent<T0, T1>;
            eventWithType.RemoveListener(callback);
        }
    }


}

