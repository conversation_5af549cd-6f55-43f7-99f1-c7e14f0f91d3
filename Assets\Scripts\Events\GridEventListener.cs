using UnityEngine;
using GridSystem;

/// <summary>
/// Grid sistemindeki event'leri dinleyen örnek sınıf
/// Diğer sistemler bu sınıfı referans alarak kendi listener'larını oluşturabilir
/// </summary>
public class GridEventListener : MonoBehaviour
{
    [Header("Debug Settings")]
    public bool enableDebugLogs = true;

    private void Start()
    {
        // Grid operasyon event'lerini dinle
        EventManager.AddListener<PooledObject>("ObjectPlaced", OnObjectPlaced);
        EventManager.AddListener<PlaceableObjectData>("ObjectRemoved", OnObjectRemoved);
        EventManager.AddListener<PooledObject>("ObjectMovedBetweenGrids", OnObjectMovedBetweenGrids);
        EventManager.AddListener<PooledObject>("ObjectMovedWithinGrid", OnObjectMovedWithinGrid);
        
        // UI event'lerini dinle
        EventManager.AddListener<PlaceableObjectData>("ObjectSelected", OnObjectSelected);
        EventManager.AddListener("ObjectSelectionCleared", OnObjectSelectionCleared);
        EventManager.AddListener<bool>("DeleteModeChanged", OnDeleteModeChanged);
        
        // Hover event'lerini dinle
        EventManager.AddListener<PooledObject>("ObjectHoverStart", OnObjectHoverStart);
        EventManager.AddListener<PooledObject>("ObjectHoverEnd", OnObjectHoverEnd);
    }

    private void OnDestroy()
    {
        // Event listener'larını temizle
        EventManager.RemoveListener<PooledObject>("ObjectPlaced", OnObjectPlaced);
        EventManager.RemoveListener<PlaceableObjectData>("ObjectRemoved", OnObjectRemoved);
        EventManager.RemoveListener<PooledObject>("ObjectMovedBetweenGrids", OnObjectMovedBetweenGrids);
        EventManager.RemoveListener<PooledObject>("ObjectMovedWithinGrid", OnObjectMovedWithinGrid);
        
        EventManager.RemoveListener<PlaceableObjectData>("ObjectSelected", OnObjectSelected);
        EventManager.RemoveListener("ObjectSelectionCleared", OnObjectSelectionCleared);
        EventManager.RemoveListener<bool>("DeleteModeChanged", OnDeleteModeChanged);
        
        EventManager.RemoveListener<PooledObject>("ObjectHoverStart", OnObjectHoverStart);
        EventManager.RemoveListener<PooledObject>("ObjectHoverEnd", OnObjectHoverEnd);
    }

    // Grid operasyon event handler'ları
    private void OnObjectPlaced(PooledObject placedObject)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"[GridEventListener] Object placed: {placedObject.name} at position {placedObject.transform.position}");
        }
        
        // Burada nesne yerleştirildiğinde yapılacak işlemler
        // Örnek: Ses efekti çal, UI güncelle, istatistik kaydet, vb.
    }

    private void OnObjectRemoved(PlaceableObjectData objectData)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"[GridEventListener] Object removed: {objectData.objectName}");
        }
        
        // Burada nesne silindiğinde yapılacak işlemler
        // Örnek: Ses efekti çal, UI güncelle, istatistik kaydet, vb.
    }

    private void OnObjectMovedBetweenGrids(PooledObject movedObject)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"[GridEventListener] Object moved between grids: {movedObject.name}");
        }
        
        // Burada nesne gridler arası taşındığında yapılacak işlemler
    }

    private void OnObjectMovedWithinGrid(PooledObject movedObject)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"[GridEventListener] Object moved within grid: {movedObject.name}");
        }
        
        // Burada nesne aynı grid içinde taşındığında yapılacak işlemler
    }

    // UI event handler'ları
    private void OnObjectSelected(PlaceableObjectData selectedObject)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"[GridEventListener] Object selected: {selectedObject.objectName}");
        }
        
        // Burada nesne seçildiğinde yapılacak işlemler
        // Örnek: Seçili nesne bilgilerini göster, preview oluştur, vb.
    }

    private void OnObjectSelectionCleared()
    {
        if (enableDebugLogs)
        {
            Debug.Log("[GridEventListener] Object selection cleared");
        }
        
        // Burada nesne seçimi kaldırıldığında yapılacak işlemler
        // Örnek: Preview'ı kaldır, UI'ı temizle, vb.
    }

    private void OnDeleteModeChanged(bool isDeleteMode)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"[GridEventListener] Delete mode changed: {isDeleteMode}");
        }
        
        // Burada silme modu değiştiğinde yapılacak işlemler
        // Örnek: Cursor değiştir, UI renk değiştir, vb.
    }

    // Hover event handler'ları
    private void OnObjectHoverStart(PooledObject hoveredObject)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"[GridEventListener] Hover started: {hoveredObject.name}");
        }
        
        // Burada nesne üzerine hover başladığında yapılacak işlemler
        // Örnek: Tooltip göster, nesne bilgilerini göster, vb.
    }

    private void OnObjectHoverEnd(PooledObject hoveredObject)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"[GridEventListener] Hover ended: {hoveredObject.name}");
        }
        
        // Burada nesne üzerinden hover kaldırıldığında yapılacak işlemler
        // Örnek: Tooltip gizle, vb.
    }
}
