using UnityEngine;
using GridSystem;

namespace GridSystem.Events
{
    // Grid operasyon event data sınıfları
    [System.Serializable]
    public class ObjectPlacedEventData
    {
        public PooledObject placedObject;
        public Vector2Int gridPosition;
        public GridPlacementSystem grid;
        public PlaceableObjectData objectData;
        
        public ObjectPlacedEventData(PooledObject obj, Vector2Int pos, GridPlacementSystem gridSystem, PlaceableObjectData data)
        {
            placedObject = obj;
            gridPosition = pos;
            grid = gridSystem;
            objectData = data;
        }
    }

    [System.Serializable]
    public class ObjectRemovedEventData
    {
        public Vector2Int gridPosition;
        public GridPlacementSystem grid;
        public PlaceableObjectData objectData;
        
        public ObjectRemovedEventData(Vector2Int pos, GridPlacementSystem gridSystem, PlaceableObjectData data)
        {
            gridPosition = pos;
            grid = gridSystem;
            objectData = data;
        }
    }

    [System.Serializable]
    public class ObjectMovedEventData
    {
        public PooledObject movedObject;
        public Vector2Int fromPosition;
        public Vector2Int toPosition;
        public GridPlacementSystem fromGrid;
        public GridPlacementSystem toGrid;
        public PlaceableObjectData objectData;
        
        public ObjectMovedEventData(PooledObject obj, Vector2Int from, Vector2Int to, 
            GridPlacementSystem fromGridSystem, GridPlacementSystem toGridSystem, PlaceableObjectData data)
        {
            movedObject = obj;
            fromPosition = from;
            toPosition = to;
            fromGrid = fromGridSystem;
            toGrid = toGridSystem;
            objectData = data;
        }
    }

    [System.Serializable]
    public class ObjectHoverEventData
    {
        public PooledObject hoveredObject;
        public Vector2Int gridPosition;
        public GridPlacementSystem grid;
        public bool isHovering; // true = hover start, false = hover end
        
        public ObjectHoverEventData(PooledObject obj, Vector2Int pos, GridPlacementSystem gridSystem, bool hovering)
        {
            hoveredObject = obj;
            gridPosition = pos;
            grid = gridSystem;
            isHovering = hovering;
        }
    }

    // Event isimleri için static class
    public static class GridEventNames
    {
        public const string OBJECT_PLACED = "ObjectPlaced";
        public const string OBJECT_REMOVED = "ObjectRemoved";
        public const string OBJECT_MOVED = "ObjectMoved";
        public const string OBJECT_HOVER_CHANGED = "ObjectHoverChanged";
        public const string GRID_SELECTION_CHANGED = "GridSelectionChanged";
        public const string PLACEMENT_MODE_CHANGED = "PlacementModeChanged";
        public const string DELETE_MODE_CHANGED = "DeleteModeChanged";
    }
}
