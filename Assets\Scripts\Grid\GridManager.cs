using UnityEngine;
using UnityEngine.UI;
using TMPro;
using UnityEditor;

namespace GridSystem
{
    [RequireComponent(typeof(GridRenderer))]
    [RequireComponent(typeof(GridPlacementSystem))]
    public class GridManager : MonoBehaviour
    {
        [Header("Grid Settings")]
        public float cellSize = 1f;
        public Vector2Int gridSize = new Vector2Int(10, 10);
        public bool showDebugLines = false;

        [Header("Status Display")]
        public TextMeshProUGUI statusNameText;
        public Image statusIconImage;
        public GridStatusData[] statusDataList = new GridStatusData[3];

        private GridStatusType currentStatus = GridStatusType.Placement;
        
        private const string GROUND_LAYER_NAME = "Ground";
        private const int GROUND_LAYER_INDEX = 7;
        
        private BoxCollider gridCollider;
        private GridRenderer gridRenderer;
        private GridPlacementSystem placementSystem;

        void Awake()
        {
            SetupComponents();
            SetupGridCollider();
            SetupLayerAndInputHandler();
        }

        private void SetupComponents()
        {
            gridRenderer = GetComponent<GridRenderer>();
            placementSystem = GetComponent<GridPlacementSystem>();

            // Status data listesini başlat
            InitializeStatusData();
        }

        private void InitializeStatusData()
        {
            if (statusDataList == null || statusDataList.Length != 3)
            {
                statusDataList = new GridStatusData[3];
            }

            // Default değerler oluştur (eğer null ise)
            if (statusDataList[0] == null)
            {
                statusDataList[0] = new GridStatusData();
                statusDataList[0].statusName = "Yerleştirme";
                statusDataList[0].description = "Nesne yerleştirme modu";
            }

            if (statusDataList[1] == null)
            {
                statusDataList[1] = new GridStatusData();
                statusDataList[1].statusName = "Taşıma";
                statusDataList[1].description = "Nesne taşıma modu";
            }

            if (statusDataList[2] == null)
            {
                statusDataList[2] = new GridStatusData();
                statusDataList[2].statusName = "Silme";
                statusDataList[2].description = "Nesne silme modu";
            }
        }

        private void SetupGridCollider()
        {
            gridCollider = gameObject.GetComponent<BoxCollider>();
            if (gridCollider == null)
            {
                gridCollider = gameObject.AddComponent<BoxCollider>();
            }
            UpdateGridCollider();
        }

        private void Start()
        {
            // EventManager listener'larını ekle
            EventManager.AddListener<PlaceableObjectData>("ObjectSelected", OnObjectSelected);
            EventManager.AddListener("ObjectSelectionCleared", OnObjectSelectionCleared);
            EventManager.AddListener<bool>("DeleteModeChanged", OnDeleteModeChanged);
            EventManager.AddListener<PooledObject>("ObjectMovedBetweenGrids", OnObjectMoved);
            EventManager.AddListener<PooledObject>("ObjectMovedWithinGrid", OnObjectMoved);
            EventManager.AddListener<PooledObject>("ObjectMoveStarted", OnObjectMoveStarted);

            // Başlangıç durumunu ayarla
            UpdateStatusDisplay(GridStatusType.Placement);
        }

        private void OnDestroy()
        {
            // EventManager listener'larını kaldır
            EventManager.RemoveListener<PlaceableObjectData>("ObjectSelected", OnObjectSelected);
            EventManager.RemoveListener("ObjectSelectionCleared", OnObjectSelectionCleared);
            EventManager.RemoveListener<bool>("DeleteModeChanged", OnDeleteModeChanged);
            EventManager.RemoveListener<PooledObject>("ObjectMovedBetweenGrids", OnObjectMoved);
            EventManager.RemoveListener<PooledObject>("ObjectMovedWithinGrid", OnObjectMoved);
            EventManager.RemoveListener<PooledObject>("ObjectMoveStarted", OnObjectMoveStarted);
        }

        private void OnObjectSelected(PlaceableObjectData objectData)
        {
            SetSelectedObject(objectData);
        }

        private void OnObjectSelectionCleared()
        {
            SetSelectedObject(null);
            UpdateStatusDisplay(GridStatusType.Placement);
        }

        private void OnDeleteModeChanged(bool isDeleteMode)
        {
            if (isDeleteMode)
            {
                UpdateStatusDisplay(GridStatusType.Deleting);
            }
            else
            {
                UpdateStatusDisplay(GridStatusType.Placement);
            }
        }

        private void OnObjectMoveStarted(PooledObject movingObject)
        {
            UpdateStatusDisplay(GridStatusType.Moving);
        }

        private void OnObjectMoved(PooledObject movedObject)
        {
            // Taşıma işlemi tamamlandığında placement moduna dön
            UpdateStatusDisplay(GridStatusType.Placement);
        }

        public void UpdateStatusDisplay(GridStatusType statusType)
        {
            currentStatus = statusType;

            if (statusDataList != null && (int)statusType < statusDataList.Length)
            {
                GridStatusData statusData = statusDataList[(int)statusType];

                if (statusData != null)
                {
                    // Status name güncelle
                    if (statusNameText != null)
                    {
                        statusNameText.text = statusData.statusName;
                    }

                    // Status icon güncelle
                    if (statusIconImage != null && statusData.statusIcon != null)
                    {
                        statusIconImage.sprite = statusData.statusIcon;
                        statusIconImage.enabled = true;
                    }
                    else if (statusIconImage != null)
                    {
                        statusIconImage.enabled = false;
                    }
                }
            }
        }

        public GridStatusType GetCurrentStatus()
        {
            return currentStatus;
        }

        private void UpdateGridCollider()
        {
            if (gridCollider == null) return;

            float width = gridSize.x * cellSize;
            float length = gridSize.y * cellSize;
            
            gridCollider.size = new Vector3(width, 0.1f, length);
            gridCollider.center = new Vector3(width / 2f, 0f, length / 2f);
            
            gameObject.layer = GROUND_LAYER_INDEX;
        }

        private void SetupLayerAndInputHandler()
        {
    #if UNITY_EDITOR
            SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
            SerializedProperty layers = tagManager.FindProperty("layers");

            SerializedProperty layerSP = layers.GetArrayElementAtIndex(GROUND_LAYER_INDEX);
            if (string.IsNullOrEmpty(layerSP.stringValue))
            {
                layerSP.stringValue = GROUND_LAYER_NAME;
                tagManager.ApplyModifiedProperties();
            }
    #endif

            var inputHandler = FindObjectOfType<PlacementInputHandler>();
            if (inputHandler != null)
            {
                int targetMask = 1 << GROUND_LAYER_INDEX;
                if (inputHandler.groundLayer != targetMask)
                {
                    inputHandler.groundLayer = targetMask;
                }
            }
            else
            {
                Debug.LogError("PlacementInputHandler bulunamadı!");
            }
        }

        void OnValidate()
        {
            if (Application.isPlaying)
            {
                UpdateGridCollider();
            }
        }

        public void SetSelectedObject(PlaceableObjectData objectData)
        {
            if (placementSystem != null)
            {
                placementSystem.SetSelectedObject(objectData);
            }
        }

        public bool TryPlaceObject(Vector3 worldPosition)
        {
            if (placementSystem != null)
            {
                return placementSystem.TryPlaceObject(worldPosition);
            }
            return false;
        }

        public bool TryRemoveObject(Vector3 worldPosition)
        {
            if (placementSystem != null)
            {
                return placementSystem.TryRemoveObject(worldPosition);
            }
            return false;
        }
    }
}