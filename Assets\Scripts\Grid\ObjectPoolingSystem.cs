using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace GridSystem
{
    public class ObjectPoolingSystem : MonoBehaviour
    {
        [Header("Pool Settings")]
        public int poolSize = 100;
        public bool expandPool = true;
        public int maxPoolSize = 500;
        
        [Header("Camera Culling")]
        public Camera mainCamera;
        public float cullDistance = 20f;
        
        private Queue<PooledObject> availableObjects = new Queue<PooledObject>();
        private List<PooledObject> activeObjects = new List<PooledObject>();
        private Dictionary<Vector2Int, PooledObject> gridToObject = new Dictionary<Vector2Int, PooledObject>();
        
        private Transform poolContainer;
        private Coroutine cullingCoroutine;
        
        private void Awake()
        {
            if (mainCamera == null)
                mainCamera = Camera.main;
                
            SetupPoolContainer();
            InitializePool();
        }
        
        private void SetupPoolContainer()
        {
            poolContainer = new GameObject("Object Pool Container").transform;
            poolContainer.SetParent(transform);
        }
        
        private void InitializePool()
        {
            for (int i = 0; i < poolSize; i++)
            {
                CreatePooledObject();
            }
        }
        
        private PooledObject CreatePooledObject()
        {
            GameObject obj = new GameObject("Pooled Object");
            obj.transform.SetParent(poolContainer);
            obj.SetActive(false);
            
            MeshRenderer meshRenderer = obj.AddComponent<MeshRenderer>();
            MeshFilter meshFilter = obj.AddComponent<MeshFilter>();
            
            PooledObject pooledObj = obj.AddComponent<PooledObject>();
            pooledObj.Initialize(meshRenderer, meshFilter);
            
            availableObjects.Enqueue(pooledObj);
            return pooledObj;
        }
        
        public PooledObject GetObject(PlaceableObjectData objectData, Vector3 position, Vector2Int gridPosition)
        {
            PooledObject pooledObj = null;
            
            if (availableObjects.Count > 0)
            {
                pooledObj = availableObjects.Dequeue();
            }
            else if (expandPool && activeObjects.Count < maxPoolSize)
            {
                pooledObj = CreatePooledObject();
            }
            else
            {
                return null;
            }
            
            pooledObj.gameObject.SetActive(true);
            pooledObj.transform.position = position;
            pooledObj.SetObjectData(objectData);
            pooledObj.SetGridPosition(gridPosition);
            
            activeObjects.Add(pooledObj);
            gridToObject[gridPosition] = pooledObj;
            
            return pooledObj;
        }
        
        public void ReturnObject(PooledObject pooledObj)
        {
            if (pooledObj == null) return;
            
            pooledObj.gameObject.SetActive(false);
            pooledObj.transform.SetParent(poolContainer);
            pooledObj.ClearData();
            
            activeObjects.Remove(pooledObj);
            
            if (gridToObject.ContainsValue(pooledObj))
            {
                var keysToRemove = new List<Vector2Int>();
                foreach (var kvp in gridToObject)
                {
                    if (kvp.Value == pooledObj)
                        keysToRemove.Add(kvp.Key);
                }
                
                foreach (var key in keysToRemove)
                {
                    gridToObject.Remove(key);
                }
            }
            
            availableObjects.Enqueue(pooledObj);
        }
        
        public bool RemoveObjectAtGrid(Vector2Int gridPosition)
        {
            if (gridToObject.TryGetValue(gridPosition, out PooledObject pooledObj))
            {
                if (pooledObj != null)
                {
                    pooledObj.PlayDestructionAnimation(() => ReturnObject(pooledObj));
                }
                else
                {
                    ReturnObject(pooledObj);
                }
                return true;
            }
            return false;
        }

        public PooledObject GetObjectAtCell(Vector2Int cell)
        {
            foreach (var kvp in gridToObject)
            {
                PooledObject obj = kvp.Value;
                if (obj == null || !obj.gameObject.activeInHierarchy) continue;

                PlaceableObjectData data = obj.GetObjectData();
                if (data == null) continue;

                Vector2Int objBase = kvp.Key;
                int w = data.size.x;
                int h = data.size.y;

                // Manuel bounds kontrolü - RectInt.Contains yerine
                bool isInBounds = cell.x >= objBase.x && cell.x < objBase.x + w &&
                                 cell.y >= objBase.y && cell.y < objBase.y + h;

                if (isInBounds)
                    return obj;
            }
            return null;
        }
     
        public void SetCullDistance(float distance)
        {
            cullDistance = distance;
        }
        
        public void SetMainCamera(Camera camera)
        {
            mainCamera = camera;
        }

        public PooledObject GetPooledObjectForData(PlaceableObjectData objectData)
        {
            foreach (var obj in activeObjects)
            {
                if (obj.GetObjectData() == objectData)
                {
                    return obj;
                }
            }
            return null;
        }

        public void UpdateObjectGridPosition(Vector2Int oldPosition, Vector2Int newPosition, PooledObject pooledObj)
        {
            if (gridToObject.ContainsKey(oldPosition))
            {
                gridToObject.Remove(oldPosition);
            }
            gridToObject[newPosition] = pooledObj;
        }
        
        private void OnDestroy()
        {
            if (cullingCoroutine != null)
                StopCoroutine(cullingCoroutine);
        }
    }
}
