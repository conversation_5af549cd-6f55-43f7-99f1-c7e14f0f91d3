using UnityEngine;
using GridSystem;
using UnityEngine.EventSystems;

public class PlacementInputHandler : MonoBehaviour
{
    public GridPlacementSystem placementSystem;
    public Camera mainCamera;
    public LayerMask groundLayer;
    public ObjectPlacementUI placementUI;
    
    private CameraSetup cameraSetup;
    private bool isDragging = false;
    private Vector2 dragStartPosition;
    private const float dragThreshold = 5f;
    private EventSystem eventSystem;
    private PooledObject currentHoverObject;
    private PooledObject draggedObject;
    private GridPlacementSystem originalGrid;
    private Vector3 dragOffset;
    private Vector2Int originalGridPosition;

    private void Start()
    {
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
        }
        
        cameraSetup = mainCamera.GetComponent<CameraSetup>();
        if (cameraSetup == null)
        {
            Debug.LogError("CameraSetup component is missing on the main camera!");
        }

        eventSystem = EventSystem.current;
        if (eventSystem == null)
        {
            Debug.LogError("EventSystem not found in the scene!");
        }
    }

    private void Update()
    {
        if (IsPointerOverUI())
        {
            isDragging = false;
            ClearHover();
            return;
        }

        HandleHover();

        if (Input.GetMouseButtonDown(0))
        {
            isDragging = true;
            dragStartPosition = Input.mousePosition;

            // Grid üzerinden nesne seçimi
            Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
            {
                // Hangi grid sistemine tıklandığını bul
                GridPlacementSystem hitGrid = hit.collider.GetComponent<GridPlacementSystem>();
                if (hitGrid != null)
                {
                    Vector2Int gridPos = hitGrid.GetGridPositionFromWorld(hit.point);
                    draggedObject = hitGrid.GetObjectAtCell(gridPos);
                    if (draggedObject != null)
                    {
                        // Nesnenin gerçek grid'ini object data'dan al
                        PlaceableObjectData objectData = draggedObject.GetObjectData();
                        if (objectData != null && objectData.currentGrid != null)
                        {
                            originalGrid = objectData.currentGrid;
                        }
                        else
                        {
                            originalGrid = hitGrid;
                        }

                        originalGridPosition = draggedObject.GetGridPosition();
                        dragOffset = draggedObject.transform.position - hit.point;
                        Debug.Log("Nesne seçildi: " + draggedObject.name + " Grid: " + originalGrid.name);
                    }
                }
            }
        }
        else if (Input.GetMouseButton(0) && draggedObject != null)
        {
            // Update object position while dragging
            Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
            {
                // Hangi grid sistemine sürüklendiğini bul
                GridPlacementSystem hitGrid = hit.collider.GetComponent<GridPlacementSystem>();
                if (hitGrid != null)
                {
                    // Grid hücresine snap yap
                    Vector2Int gridPos = hitGrid.GetGridPositionFromWorld(hit.point);
                    Vector3 snappedPosition = hitGrid.GridToWorldPosition(gridPos);
                    // Y eksenini koru
                    snappedPosition.y = draggedObject.transform.position.y;
                    draggedObject.transform.position = snappedPosition;
                }

                // Camera pan'ı engellemek için flag set et
                if (cameraSetup != null)
                {
                    cameraSetup.BlockPanningDuringDrag(true);
                }
            }
        }
        else if (Input.GetMouseButtonUp(0))
        {
            if (!isDragging) return;

            // Sürükleme bittiğinde pan engelini kaldır
            if (cameraSetup != null)
            {
                cameraSetup.BlockPanningDuringDrag(false);
            }

            if (draggedObject != null)
            {
                bool placementSuccessful = false;

                // Try to place on new grid
                Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
                if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
                {
                    // Find the target grid system
                    GridPlacementSystem targetGrid = hit.collider.GetComponent<GridPlacementSystem>();
                    if (targetGrid != null)
                    {
                        Vector2Int targetGridPos = targetGrid.GetGridPositionFromWorld(hit.point);
                        float originalY = draggedObject.transform.position.y;

                        if (targetGrid != originalGrid)
                        {
                            // Farklı bir gride taşıma
                            if (targetGrid.GetObjectAtCell(targetGridPos) == null)
                            {
                                // Nesnenin object data'sını al
                                PlaceableObjectData objectData = draggedObject.GetObjectData();

                                // Orijinal griddeki nesneyi sil
                                originalGrid.TryRemoveObject(draggedObject.transform.position);

                                // Hedef gride yerleştir
                                targetGrid.SetSelectedObject(objectData);
                                Vector3 newPos = targetGrid.GridToWorldPosition(targetGridPos);
                                newPos.y = originalY;

                                if (targetGrid.TryPlaceObject(newPos))
                                {
                                    placementSuccessful = true;
                                    Debug.Log("Nesne başarıyla taşındı: " + draggedObject.name);
                                }
                            }
                        }
                        else
                        {
                            // Aynı grid içinde hareket
                            if (targetGrid.TryMoveObjectWithinGrid(originalGridPosition, targetGridPos))
                            {
                                Vector3 newPos = targetGrid.GridToWorldPosition(targetGridPos);
                                newPos.y = originalY;
                                draggedObject.transform.position = newPos;
                                placementSuccessful = true;
                                Debug.Log("Nesne aynı grid içinde taşındı");
                            }
                        }
                    }
                }

                // Eğer yerleştirme başarısız olduysa orijinal pozisyona geri koy
                if (!placementSuccessful)
                {
                    Vector3 originalPos = originalGrid.GridToWorldPosition(originalGridPosition);
                    originalPos.y = draggedObject.transform.position.y;
                    draggedObject.transform.position = originalPos;
                    Debug.Log("Nesne orijinal pozisyonuna geri döndürüldü");
                }

                draggedObject = null;
                originalGrid = null;
            }
            else
            {
                // Original click handling
                float dragDistance = Vector2.Distance(dragStartPosition, Input.mousePosition);
                bool isDeleteMode = placementUI != null && placementUI.IsDeleteModeActive();

                Debug.Log($"Click handling - DragDistance: {dragDistance}, Threshold: {dragThreshold}, IsPanning: {cameraSetup.IsPanning}, DeleteMode: {isDeleteMode}");

                if (dragDistance < dragThreshold && !cameraSetup.IsPanning)
                {
                    if (isDeleteMode)
                    {
                        Debug.Log("Calling TryDeleteObject");
                        TryDeleteObject();
                    }
                    else
                    {
                        Debug.Log("Calling TryPlaceObject");
                        TryPlaceObject();
                    }
                }
                else
                {
                    Debug.Log($"Click ignored - DragDistance: {dragDistance} >= {dragThreshold} OR IsPanning: {cameraSetup.IsPanning}");
                }
            }

            isDragging = false;
        }
    }

    private bool IsPointerOverUI()
    {
        // Touch kontrolü
        if (Input.touchCount > 0)
        {
            return EventSystem.current.IsPointerOverGameObject(Input.GetTouch(0).fingerId);
        }
        
        // Mouse kontrolü
        return EventSystem.current.IsPointerOverGameObject();
    }

    private void TryPlaceObject()
    {
        Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);

        if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
        {
            placementSystem.TryPlaceObject(hit.point);
        }
    }

    private void HandleHover()
    {
        if (placementUI != null && placementUI.IsDeleteModeActive())
        {
            Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);

            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
            {
                // Hangi grid sistemine hover yapıldığını bul
                GridPlacementSystem hitGrid = hit.collider.GetComponent<GridPlacementSystem>();
                if (hitGrid != null)
                {
                    Vector2Int gridPos = hitGrid.GetGridPositionFromWorld(hit.point);
                    PooledObject hoverObj = hitGrid.GetObjectAtCell(gridPos);

                    Debug.Log($"Hover - Grid: {hitGrid.name}, GridPos: {gridPos}, HoverObj: {(hoverObj != null ? hoverObj.name : "null")}");

                    if (hoverObj != currentHoverObject)
                    {
                        ClearHover();
                        currentHoverObject = hoverObj;
                        if (currentHoverObject != null)
                        {
                            currentHoverObject.SetHighlight(true);
                            Debug.Log($"Highlight set for: {currentHoverObject.name}");
                        }
                    }
                }
                else
                {
                    Debug.Log("No GridPlacementSystem found on hit collider");
                    ClearHover();
                }
            }
            else
            {
                ClearHover();
            }
        }
        else
        {
            ClearHover();
        }
    }

    private void ClearHover()
    {
        if (currentHoverObject != null)
        {
            currentHoverObject.SetHighlight(false);
            currentHoverObject = null;
        }
    }

    private void TryDeleteObject()
    {
        Debug.Log($"TryDeleteObject called. CurrentHoverObject: {(currentHoverObject != null ? currentHoverObject.name : "null")}");

        if (currentHoverObject != null)
        {
            // Nesnenin hangi grid'de olduğunu object data'dan öğren
            PlaceableObjectData objectData = currentHoverObject.GetObjectData();
            GridPlacementSystem targetGrid = null;

            Debug.Log($"ObjectData: {(objectData != null ? objectData.objectName : "null")}, CurrentGrid: {(objectData?.currentGrid != null ? objectData.currentGrid.name : "null")}");

            if (objectData != null && objectData.currentGrid != null)
            {
                targetGrid = objectData.currentGrid;
                Debug.Log($"Using currentGrid: {targetGrid.name}");
            }
            else
            {
                // Fallback: raycast ile grid'i bul
                Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
                if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
                {
                    targetGrid = hit.collider.GetComponent<GridPlacementSystem>();
                    Debug.Log($"Using raycast grid: {(targetGrid != null ? targetGrid.name : "null")}");
                }
            }

            if (targetGrid != null)
            {
                Vector3 worldPos = currentHoverObject.transform.position;
                bool removeResult = targetGrid.TryRemoveObject(worldPos);
                Debug.Log($"Remove result: {removeResult}, Nesne: {currentHoverObject.name}, Grid: {targetGrid.name}, WorldPos: {worldPos}");
            }
            else
            {
                Debug.LogError("TargetGrid is null! Cannot delete object.");
            }

            ClearHover();
        }
        else
        {
            Debug.LogWarning("No hover object to delete!");
        }
    }
}
